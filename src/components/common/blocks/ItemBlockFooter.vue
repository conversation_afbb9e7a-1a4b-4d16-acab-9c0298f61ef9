<script setup lang="ts">
import { ref, computed } from 'vue';
import ToggleBtn from '../ToggleBtn.vue';
import type { Option } from 'src/types/models';

const props = defineProps<{
  label: string;
  isRequired?: boolean;
  type?: string;
  options?: Option[];
  availableSections?: { label: string; value: number }[];
}>();

const emit = defineEmits<{
  (e: 'duplicate'): void;
  (e: 'delete'): void;
  (e: 'update:isRequired', value: boolean): void;
  (e: 'click'): void;
  (e: 'update:option-next-section', optionId: number, nextSection: number | null): void;
}>();

// State for showing section navigation dropdowns
const showSectionDropdowns = ref(false);

// Computed property for section options
const sectionOptions = computed(() => {
  return props.availableSections || [];
});

// Handle toggle change
function handleToggleChange(value: boolean | undefined) {
  // Ensure we always emit a boolean value, defaulting to false if undefined
  const booleanValue = value ?? false;
  emit('update:isRequired', booleanValue);
}

// Handle open_in_new button click
function handleOpenInNewClick() {
  showSectionDropdowns.value = !showSectionDropdowns.value;
}

// Handle section selection for an option
function handleSectionSelect(optionId: number, sectionValue: number | null) {
  emit('update:option-next-section', optionId, sectionValue);
}
</script>
<template>
  <div class="footer-container">
    <div
      class="row items-center justify-end"
      style="max-height: 50px; width: 100%"
      @click="$emit('click')"
    >
      <q-btn
        v-if="props.type === 'RADIO'"
        flat
        round
        icon="open_in_new"
        padding="sm"
        class="bg-transparent"
        color="grey"
        @click.stop="handleOpenInNewClick"
      />
      <q-btn
        flat
        round
        icon="content_copy"
        padding="sm"
        class="bg-transparent"
        color="grey"
        @click="$emit('duplicate')"
      />
      <q-btn
        flat
        round
        icon="delete"
        color="grey"
        padding="sm"
        class="bg-transparent"
        @click="$emit('delete')"
      />
      <q-separator vertical inset color="#898989" />
      <q-item-label style="padding: 10px">{{ label }}</q-item-label>
      <ToggleBtn
        :model-value="props.isRequired || false"
        @update:model-value="handleToggleChange"
        @click="$emit('click')"
      />
    </div>

    <!-- Section Navigation Dropdowns -->
    <div
      v-if="showSectionDropdowns && props.type === 'RADIO' && props.options"
      class="section-dropdowns q-mt-md"
    >
      <div class="text-caption text-grey-7 q-mb-sm">
        Configure section navigation for each option:
      </div>
      <div v-for="option in props.options" :key="option.id" class="option-dropdown-row q-mb-sm">
        <div class="row items-center q-gutter-sm">
          <div class="option-text">{{ option.optionText || `Option ${option.sequence}` }}</div>
          <q-select
            :model-value="option.nextSection"
            :options="sectionOptions"
            emit-value
            map-options
            outlined
            dense
            clearable
            placeholder="Default (sequential)"
            style="min-width: 200px"
            @update:model-value="(value) => handleSectionSelect(option.id, value)"
          >
            <template #selected>
              <span v-if="option.nextSection">Section {{ option.nextSection }}</span>
              <span v-else class="text-grey-6">Default (sequential)</span>
            </template>
          </q-select>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
.footer-container {
  width: 100%;
}

.section-dropdowns {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e0e0e0;
}

.option-dropdown-row {
  background-color: white;
  border-radius: 4px;
  padding: 8px;
  border: 1px solid #e0e0e0;
}

.option-text {
  min-width: 120px;
  font-weight: 500;
  color: #424242;
}
</style>
