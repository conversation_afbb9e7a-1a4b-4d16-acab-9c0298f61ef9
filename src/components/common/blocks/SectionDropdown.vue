<template>
  <div class="section-dropdown-container">
    <div class="text-caption text-grey-7 q-mb-sm">
      Configure section navigation for each option:
    </div>
    <div v-for="option in options" :key="option.id" class="option-dropdown-row q-mb-sm">
      <div class="row items-center q-gutter-sm">
        <div class="option-text">{{ option.optionText || `Option ${option.sequence}` }}</div>
        <q-select
          :model-value="option.nextSection"
          :options="sectionOptions"
          emit-value
          map-options
          outlined
          dense
          clearable
          placeholder="Default (sequential)"
          style="min-width: 200px"
          @update:model-value="(value) => handleSectionSelect(option.id, value)"
        >
          <template #selected>
            <span v-if="option.nextSection">Section {{ option.nextSection }}</span>
            <span v-else class="text-grey-6">Default (sequential)</span>
          </template>
        </q-select>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { Option } from 'src/types/models';

const props = defineProps<{
  options: Option[];
  availableSections: { label: string; value: number }[];
}>();

const emit = defineEmits<{
  (e: 'update:option-next-section', optionId: number, nextSection: number | null): void;
}>();

// Computed property for section options
const sectionOptions = computed(() => {
  return props.availableSections || [];
});

// Handle section selection for an option
function handleSectionSelect(optionId: number, sectionValue: number | null) {
  emit('update:option-next-section', optionId, sectionValue);
}
</script>

<style scoped>
.section-dropdown-container {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e0e0e0;
}

.option-dropdown-row {
  background-color: white;
  border-radius: 4px;
  padding: 8px;
  border: 1px solid #e0e0e0;
}

.option-text {
  min-width: 120px;
  font-weight: 500;
  color: #424242;
}
</style>
